# send_email.py
import os
import smtplib
from email.mime.text import MIMEText
# from config import settings

# SMTP_HOST = settings.SMTP_HOST
# SMTP_PORT = settings.SMTP_PORT
# SMTP_USER = settings.SMTP_USER
# SMTP_PASS = settings.SMTP_PASS

from dotenv import load_dotenv

load_dotenv()

# SMTP_HOST = os.getenv("SMTP_HOST", "smtp.office365.com")
# SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
# SMTP_USER = os.getenv("SMTP_USER")
# SMTP_PASS = os.getenv("SMTP_PASS")

# SMTP_HOST='smtppro.zoho.com'
SMTP_HOST='smtp.zoho.com'
SMTP_PORT=587
SMTP_USER = "<EMAIL>"
SMTP_PASS = "uj9RQ?ekf0Nw;SPz+$p6"

def send_email(to_email, subject, body, from_email=None):
    if from_email is None:
        from_email = SMTP_USER
    msg = MIMEText(body)
    msg["Subject"] = subject
    msg["From"] = from_email
    msg["To"] = to_email

    with smtplib.SMTP(SMTP_HOST, SMTP_PORT, timeout=30) as server:
        server.ehlo()
        server.starttls()
        server.ehlo()
        server.login(SMTP_USER, SMTP_PASS)
        server.sendmail(from_email, [to_email], msg.as_string())

if __name__ == "__main__":
    # simple quick test run; customize as you wish
    to = '<EMAIL>'
    send_email(to, "ُE-Mail Test", "we can test Now!!!!.")
    print("✅ Sent to:", to)