# main.py
from contextlib import asynccontextmanager
from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi_events.handlers.local import local_handler
from fastapi_events.middleware import EventHandlerASGIMiddleware
from fastapi.middleware.cors import CORSMiddleware

######### Rate Limiter#######
from slowapi import Limiter
from slowapi.util import get_remote_address

from config import settings

from src.routes.auth import auth_router
from src.routes.contact_us import contact_router
from src.routes.general import general_router
from src.routes.profile import profile_router
from src.routes.assessments import assessments_router
from src.routes.session import session_router
from src.routes.course import course_router
from src.routes.admin_stats import admin_stats_router
from src.routes.user_stats import user_stats_router

from src.utils.cronjobs import start_cron_jobs
from src.utils.global_store import set_request
from src.utils.logger import AdvancedLogger
from src.utils.security_middleware import SecurityMiddleware
from src.utils.handle_router_exceptions import http_exception_handler, validation_exception_handler

logger = AdvancedLogger(__name__)




# FastAPI lifespan event
@asynccontextmanager
async def lifespan(application: FastAPI):
    try:
        # start_cron_jobs()
        # messenger = await WhatsAppMessenger.get_instance()
        # application.state.whatsapp_messenger = messenger
        # import asyncio
        # asyncio.create_task(messenger.run())
        yield
    finally:
        pass


common_args = {
    "lifespan": lifespan,
    "title": "Mr.John - Course server",
    "description": "Mr.John - Course server - Courses and assessments",
    "version": settings.APP_VERSION,
    "root_path": '/api/v1',
}

common_args.update({
    "openapi_url": "/openapi.json" if settings.ENVIRONMENT != 'production' else "",
    "docs_url": "/docs" if settings.ENVIRONMENT != 'production' else "",
})

app = FastAPI(**common_args)


############Defined the rate limit middleware##########

limiter = Limiter(key_func=get_remote_address)

if settings.ENVIRONMENT == 'production':
    cors = settings.BACKEND_CORS_ORIGINS
else:
    cors = ["*"]


app.add_middleware(
    CORSMiddleware,
    allow_origins=cors,  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)
app.add_middleware(EventHandlerASGIMiddleware, handlers=[local_handler])

app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)

@app.middleware("http")
async def set_global_data(request: Request, call_next):
    # Set request data in the global storage
    set_request(request)
    response = await call_next(request)
    response.headers["X-Robots-Tag"] = "noindex, nofollow"  # Add the X-Robots-Tag header to disallow indexing
    return response


@app.get("/")
async def root():
    return {
        "status": settings.APP_STATUS,
        "version": settings.APP_VERSION,
    }


@app.get("/health", tags=["Health"])
def health_check():
    return {"Health": f"status: {settings.APP_STATUS}, version: {settings.APP_VERSION}"}


app.include_router(router=general_router)
app.include_router(router=auth_router)
app.include_router(router=profile_router)
app.include_router(router=contact_router)
app.include_router(router=assessments_router)
app.include_router(router=session_router)
app.include_router(router=course_router)
app.include_router(admin_stats_router)
app.include_router(user_stats_router)


# Add the security middleware
app.add_middleware(SecurityMiddleware)



#https://chatgpt.com/share/e/68c1e886-3878-8001-b9dd-8b56be56efe5