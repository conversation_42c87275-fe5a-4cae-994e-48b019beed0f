# src/services/user_stats.py
from __future__ import annotations

from typing import List, Dict, Iterable
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, case, Integer

from src.DB.models.assessments import (
    Course, CourseContent, Lesson, Assessment,
    UserLessonProgress, UserSession, Response, Option
)
from src.schemas.stats import (
    CourseProgressRead, UserAssessmentStat, UserLessonStat
)


# ---------- Helpers ----------

async def _course_content_counts(db: AsyncSession, course_id: UUID) -> int:
    res = await db.execute(
        select(func.count(CourseContent.content_id))
        .where(CourseContent.course_id == course_id)
    )
    return int(res.scalar() or 0)


# ---------- Course progress (per user) ----------

async def get_course_progress(db: AsyncSession, user_id: UUID, course_id: UUID) -> CourseProgressRead:
    # Fetch title
    r_title = await db.execute(select(Course.title).where(Course.course_id == course_id))
    title = r_title.scalar() or "Unknown"

    # Total contents in course
    total_contents_res = await db.execute(
        select(
            func.count(CourseContent.content_id).label("total"),
            func.sum(case((CourseContent.lesson_id.isnot(None), 1), else_=0)).label("lessons_cnt"),
            func.sum(case((CourseContent.assessment_id.isnot(None), 1), else_=0)).label("assess_cnt"),
        ).where(CourseContent.course_id == course_id)
    )
    total_contents, _, _ = total_contents_res.fetchone() or (0, 0, 0)
    total_contents = int(total_contents or 0)

    # Completed lessons (by UserLessonProgress.completed)
    lessons_ids_res = await db.execute(
        select(CourseContent.lesson_id)
        .where(CourseContent.course_id == course_id, CourseContent.lesson_id.isnot(None))
    )
    lesson_ids = [row[0] for row in lessons_ids_res.fetchall()]
    completed_lessons = 0
    if lesson_ids:
        c_res = await db.execute(
            select(func.count(UserLessonProgress.lesson_id))
            .where(
                UserLessonProgress.user_id == user_id,
                UserLessonProgress.lesson_id.in_(lesson_ids),
                UserLessonProgress.completed.is_(True)
            )
        )
        completed_lessons = int(c_res.scalar() or 0)

    # Completed assessments (has completed session)
    assess_ids_res = await db.execute(
        select(CourseContent.assessment_id)
        .where(CourseContent.course_id == course_id, CourseContent.assessment_id.isnot(None))
    )
    assess_ids = [row[0] for row in assess_ids_res.fetchall()]

    completed_assessments = 0
    if assess_ids:
        a_res = await db.execute(
            select(func.count(UserSession.session_id))
            .where(
                UserSession.user_id == user_id,
                UserSession.assessment_id.in_(assess_ids),
                UserSession.completed_at.isnot(None)
            )
        )
        completed_assessments = int(a_res.scalar() or 0)

    denom = total_contents or 1
    progress_pct = round(((completed_lessons + completed_assessments) / denom) * 100.0, 2)

    return CourseProgressRead(
        course_id=course_id,
        title=title,
        total_contents=total_contents,
        completed_lessons=completed_lessons,
        completed_assessments=completed_assessments,
        progress_pct=progress_pct
    )


async def get_all_courses_progress(db: AsyncSession, user_id: UUID) -> List[CourseProgressRead]:
    res = await db.execute(select(Course.course_id))
    course_ids = [row[0] for row in res.fetchall()]
    out: List[CourseProgressRead] = []
    for cid in course_ids:
        out.append(await get_course_progress(db, user_id, cid))
    return out


# ---------- User assessment stats ----------

async def _session_correct_ratio_map(db: AsyncSession, session_ids: Iterable[UUID]) -> Dict[UUID, float]:
    session_ids = list({sid for sid in session_ids if sid})
    if not session_ids:
        return {}
    sub = (
        select(
            Response.session_id.label("sid"),
            func.count(Response.response_id).label("total"),
            func.sum(case((Option.is_correct.is_(True), 1), else_=0).cast(Integer)).label("correct"),
        )
        .select_from(Response.__table__.outerjoin(Option, Response.option_id == Option.option_id))
        .where(Response.session_id.in_(session_ids))
        .group_by(Response.session_id)
        .subquery()
    )
    r = await db.execute(select(sub.c.sid, sub.c.total, sub.c.correct))
    out: Dict[UUID, float] = {}
    for sid, total, correct in r.fetchall():
        total = total or 0
        correct = correct or 0
        out[sid] = (float(correct) / float(total)) if total else 0.0
    return out


async def get_user_assessment_stats(db: AsyncSession, user_id: UUID, pass_threshold: float = 0.6) -> List[UserAssessmentStat]:
    q = (
        select(
            UserSession.session_id,
            UserSession.assessment_id,
            UserSession.completed_at
        )
        .where(UserSession.user_id == user_id)
    )
    res = await db.execute(q)
    sess = res.fetchall()
    if not sess:
        return []

    session_ids = [row[0] for row in sess]
    ratios = await _session_correct_ratio_map(db, session_ids)

    # group by assessment
    per_assess: Dict[UUID, Dict[str, object]] = {}
    for sid, aid, completed_at in sess:
        d = per_assess.setdefault(aid, {"attempts": 0, "last": None, "best_ratio": 0.0, "passed": False})
        d["attempts"] = int(d["attempts"]) + 1
        if completed_at and (d["last"] is None or completed_at > d["last"]):  # pyright: ignore
            d["last"] = completed_at
        r = ratios.get(sid, 0.0)
        d["best_ratio"] = max(float(d["best_ratio"]), r)  # pyright: ignore
        if r >= pass_threshold:
            d["passed"] = True

    # names
    names_res = await db.execute(select(Assessment.assessment_id, Assessment.name))
    names = {row[0]: row[1] for row in names_res.fetchall()}

    out: List[UserAssessmentStat] = []
    for aid, d in per_assess.items():
        out.append(UserAssessmentStat(
            assessment_id=aid,
            name=names.get(aid, "Unknown"),
            attempts=int(d["attempts"]),
            last_attempt_at=d["last"],
            best_correct_ratio=float(d["best_ratio"]),  # 0..1
        ))
    return out


# ---------- User lesson stats ----------

async def get_user_lessons_stats(db: AsyncSession, user_id: UUID) -> List[UserLessonStat]:
    q = (
        select(Lesson.lesson_id, Lesson.title, UserLessonProgress.viewed_at, UserLessonProgress.completed)
        .outerjoin(UserLessonProgress, UserLessonProgress.lesson_id == Lesson.lesson_id)
        .where((UserLessonProgress.user_id == user_id) | (UserLessonProgress.user_id.is_(None)))
    )
    res = await db.execute(q)
    out: List[UserLessonStat] = []
    for lid, title, viewed_at, completed in res.fetchall():
        out.append(UserLessonStat(
            lesson_id=lid,
            title=title,
            viewed_at=viewed_at,
            completed=bool(completed) if completed is not None else False
        ))
    return out
