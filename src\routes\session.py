from fastapi import APIRouter, HTTPException, Request
from uuid import UUID
from typing import List

from src.services.auth import get_user
from src.utils.logger import AdvancedLogger

from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.services.assessments import get_simple_ass_by_id
from src.utils.Error_Handling import ErrorCode
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse

from src.schemas.assessments import (
    SessionCreate,
    ResponseSubmit,
    SessionResult,
    ResponseRead,
    SessionRead,
)
from src.services.session import (
    create_user_session,
    submit_assessment_responses,
    get_user_sessions,
    get_user_session_by_id,
    get_user_session_profile_by_id,
    get_user_session_response_by_id, delete_user_session, get_user_session_response_profiles,
)
logger = AdvancedLogger(name=__name__)

session_router = APIRouter(prefix="/sessions", tags=["Sessions"])

@session_router.post("/", response_model=SessionRead)
@handle_router_exceptions
async def start_session(request: Request, user_id: CurrentUserUpgrade, data: SessionCreate, db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)
    assessment = await get_simple_ass_by_id(data.assessment_id, db)
    if not assessment:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)
    return await create_user_session(user.user_id, data, db)

@session_router.get("/", response_model=List[SessionRead])
@handle_router_exceptions
async def get_all_sessions(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

    return await get_user_sessions(user.user_id, db)

@session_router.get("/{session_id}", response_model=SessionRead)
@handle_router_exceptions
async def get_session(request: Request, session_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

    return await get_user_session_by_id(user.user_id, session_id, db)

@session_router.delete("/{session_id}")
@handle_router_exceptions
async def delete_session(request: Request, user_id: CurrentUserUpgrade, session_id : UUID, db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)
    return await delete_user_session(user.user_id, session_id, db)

@session_router.post("/{session_id}/submit", response_model=SessionResult)
@handle_router_exceptions
async def submit_responses(request: Request, user_id: CurrentUserUpgrade, session_id: UUID, responses: List[ResponseSubmit], db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

    return await submit_assessment_responses(user.user_id, session_id, responses, db)

@session_router.get("/{session_id}/response", response_model=List[ResponseRead])
@handle_router_exceptions
async def get_session_response(request: Request, session_id: UUID, user_id: CurrentUserUpgrade, db: SessionDep):
    user = await get_user(db,user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

    return await get_user_session_response_by_id(user.user_id,session_id, db)

# @session_router.get("/{session_id}/profile-result", response_model=SessionResult)
# @handle_router_exceptions
# async def get_all_results(request: Request, user_id: CurrentUserUpgrade, session_id: UUID, db: SessionDep):
#     user = await get_user(db,user_id)
#     if not user:
#         raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

#     return await get_user_session_profile_by_id(session_id, db)

# @session_router.get("/profile-results/all", response_model=List[SessionResult])
# @handle_router_exceptions
# async def get_all_results(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
#     user = await get_user(db, user_id)
#     if not user:
#         raise HTTPException(status_code=404, detail=ErrorCode.THIS_USER_NOT_FOUND)

#     return await get_user_session_response_profiles(user.user_id, db)

