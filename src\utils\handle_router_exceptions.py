from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from starlette.exceptions import HTTPEx<PERSON> as StarletteHTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from jose import J<PERSON><PERSON>rror, ExpiredSignatureError
from pydantic import ValidationError, BaseModel
from functools import wraps
from typing import Callable, Union, List, Any
from .logger import AdvancedLogger
from config import settings
import traceback
import re


logger = AdvancedLogger(__name__)

class ErrorResponse(BaseModel):
    is_error: bool
    status_code: int
    detail: Union[str, list]  # Handles both string and validation errors (422)
    class Config:
        use_enum_values = True 


def handle_router_exceptions(func: Callable):
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        logger = AdvancedLogger(f"{func.__module__}.{func.__name__}")

        def json_response(status_code: int, message: str | list) -> JSONResponse:
            return JSONResponse(
                status_code=status_code,
                content=ErrorResponse(
                    is_error=True,
                    status_code=status_code,
                    detail=message
                ).model_dump()
            )

        try:
            return await func(request, *args, **kwargs)

        except (HTTPException, StarletteHTTPException) as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"HTTPException error: {str(e)}")
            return json_response(e.status_code, str(e.detail))

        except RequestValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Request validation error: {str(e)}")
            return json_response(422, e.errors())

        except ValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Validation error: {str(e)}")
            return json_response(422, e.errors())

        except ExpiredSignatureError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Authentication error: {str(e)}")
            return json_response(401, "token_has_expired")

        except JWTError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"JWT error: {str(e)}")
            return json_response(401, "invalid_token")

        except SQLAlchemyError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Database error: {str(e)}")
            return json_response(500, "Database error")

        except TimeoutError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Timeout error for {request.method} {request.url}: {str(e)}")
            return json_response(408, "request_timed_out")

        except Exception as e:
            if settings.ENVIRONMENT in ("development", "local"):
                traceback.print_exc()
                logger.error(f"Unexpected error: {str(e)}")
            return json_response(500, "Internal_server_error")

    return wrapper


async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            is_error=True,
            status_code=exc.status_code,
            detail=exc.detail
        ).model_dump()
    )

def _format_loc(loc: List[Any]) -> str:
    """
    Convert FastAPI/Pydantic loc into a dotted path.
    ["body", "items", 0, "name"] -> "items[0].name"
    """
    parts: List[str] = []
    for seg in loc:
        if isinstance(seg, int):
            # index applies to the previous key
            if parts:
                parts[-1] = f"{parts[-1]}[{seg}]"
            else:
                parts.append(f"[{seg}]")
        else:
            parts.append(str(seg))
    return ".".join(parts)

def _guess_field_from_offset(body_text: str, char_pos: int) -> str | None:
    """
    When JSON is invalid (e.g., `"version_int": ,`), FastAPI reports loc ["body", 59].
    We heuristically map that offset back to the nearest key before the error.
    """
    if not body_text or char_pos is None or char_pos < 0:
        return None
    snippet = body_text[:char_pos]
    # Find the last JSON key before the error position.
    # Matches "key":   (does not cross quotes or escaped chars)
    matches = list(re.finditer(r'"([^"\\]+)"\s*:', snippet))
    if not matches:
        return None
    return matches[-1].group(1)

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    try:
        raw = await request.body()
        body_text = raw.decode("utf-8", errors="ignore")
    except Exception:
        body_text = ""

    details: List[str] = []

    for error in exc.errors():
        err_type = error.get("type") or ""
        loc = error.get("loc", [])
        msg = error.get("msg") or "Invalid value"

        # Default path string from loc
        path = _format_loc(loc)

        # Special handling for invalid JSON (e.g. missing value after colon)
        if err_type == "json_invalid":
            # Try to replace <number> with <field>
            if len(loc) >= 2 and loc[0] == "body" and isinstance(loc[1], int):
                guessed = _guess_field_from_offset(body_text, loc[1])
                path = f"{guessed}" if guessed else "body"
            # Add ctx.error if present (e.g. "Expecting value")
            ctx_error = (error.get("ctx") or {}).get("error")
            pretty = f"{path}: JSON decode error" + (f" ({ctx_error})" if ctx_error else "")
            details.append(pretty)
            continue

        # Missing field
        if err_type == "missing":
            details.append(f"{path}: Field required")
            continue

        # Pydantic v2 parsing/type errors look like "int_parsing", "bool_parsing", etc.
        if "parsing" in err_type:
            details.append(f"{path}: {msg}")
            continue

        # Fallback for all other validation messages
        details.append(f"{path}: {msg}")

    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            is_error=True,
            status_code=422,
            detail=details if details else ["Invalid request"]
        ).model_dump()
    )
