from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from src.DB.enums import CourseContentType
from src.schemas.assessments import AssessmentRead

# ----------------------
# Course Schemas
# ----------------------

class CourseCreate(BaseModel):
    title: str = Field(..., min_length=1)
    description: Optional[str] = None
    is_active: bool = True

class CourseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class CourseRead(BaseModel):
    course_id: UUID
    title: str
    description: Optional[str]
    is_active: bool
    created_at: datetime

    # relations
    contents: Optional[List["CourseContentRead"]]

    model_config = {"from_attributes": True}


# ----------------------
# Lesson Schemas
# ----------------------

class LessonCreate(BaseModel):
    title: str
    body_md: Optional[str] = None
    video_url: Optional[str] = None
    file_url: Optional[str] = None
    content_type: CourseContentType = CourseContentType.TEXT

class LessonUpdate(BaseModel):
    title: Optional[str] = None
    body_md: Optional[str] = None
    video_url: Optional[str] = None
    file_url: Optional[str] = None
    content_type: Optional[CourseContentType] = None

class LessonRead(BaseModel):
    lesson_id: UUID
    title: str
    body_md: Optional[str]
    video_url: Optional[str]
    file_url: Optional[str]
    content_type: Optional[CourseContentType] = None # default value for new lessons
    created_at: datetime

    model_config = {"from_attributes": True}


# ----------------------
# Course Content Schemas
# ----------------------

class CourseContentCreate(BaseModel):
    content_type: CourseContentType
    sequence: int
    lesson_id: Optional[UUID] = None
    assessment_id: Optional[UUID] = None

class CourseContentUpdate(BaseModel):
    content_type: Optional[CourseContentType] = None
    lesson_id: Optional[UUID] = None
    assessment_id: Optional[UUID] = None
    sequence: Optional[int] = None

class CourseContentRead(BaseModel):
    content_id: UUID
    course_id: UUID
    content_type: CourseContentType
    sequence: int
    lesson: Optional[LessonRead]
    assessment: Optional["AssessmentRead"]

    model_config = {"from_attributes": True}


class BulkLessonCreate(BaseModel):
    lessons: List[LessonCreate]


CourseRead.model_rebuild()
CourseContentRead.model_rebuild()
